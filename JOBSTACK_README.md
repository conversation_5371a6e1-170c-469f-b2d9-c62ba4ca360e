# JobStack - Flutter Job Directory Application

A Flutter application that displays job postings from JSON data in a clean, organized directory-style layout. The application is designed to be embeddable and can be easily integrated into other applications or web pages.

## Features

- **Job Directory Display**: Shows job postings in an organized, card-based layout
- **Search Functionality**: Search through jobs by title, company, location, or description
- **Filtering Options**: Filter jobs by location and organization
- **Sorting Options**: Sort jobs by date, company name, or location
- **Responsive Design**: Works on mobile, tablet, and desktop
- **Embeddable Components**: Multiple widget configurations for different use cases
- **External Links**: Direct links to job application pages
- **Company Logos**: Displays company logos with fallback icons
- **Closing Date Indicators**: Highlights jobs that are closing soon

## Project Structure

```
lib/
├── models/
│   └── job_posting.dart          # Job data model
├── services/
│   └── job_data_service.dart     # Data loading and processing
├── widgets/
│   ├── job_card.dart             # Individual job card widget
│   ├── job_directory.dart        # Main directory widget
│   └── embeddable_job_widget.dart # Embeddable widget configurations
├── jobs/
│   └── jobs_2025-07-02T16-35-43-444Z.json # Job data file
└── main.dart                     # Main application entry point
```

## Usage Examples

### 1. Full Job Portal Application

```dart
import 'package:flutter/material.dart';
import 'package:jobstack/widgets/embeddable_job_widget.dart';

void main() {
  runApp(const JobPortalApp(
    title: 'My Job Portal',
    primaryColor: Colors.blue,
  ));
}
```

### 2. Embeddable Widget - Full Featured

```dart
import 'package:jobstack/widgets/embeddable_job_widget.dart';

// Full-featured widget with search and filters
Widget buildJobSection() {
  return EmbeddableJobWidget.full(
    title: 'Browse Jobs',
    primaryColor: Colors.green,
    height: 600,
  );
}
```

### 3. Embeddable Widget - Compact

```dart
// Compact widget with search only
Widget buildCompactJobSection() {
  return EmbeddableJobWidget.compact(
    title: 'Latest Jobs',
    maxItems: 10,
    primaryColor: Colors.orange,
    height: 500,
  );
}
```

### 4. Embeddable Widget - Minimal

```dart
// Minimal widget for sidebars
Widget buildSidebarJobs() {
  return EmbeddableJobWidget.minimal(
    title: 'Recent Jobs',
    maxItems: 5,
    height: 300,
  );
}
```

### 5. Using the Factory Methods

```dart
import 'package:jobstack/widgets/embeddable_job_widget.dart';

class MyHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Sidebar widget
          JobWidgetFactory.createSidebarWidget(
            title: 'Featured Jobs',
            maxItems: 3,
            primaryColor: Colors.purple,
          ),
          
          // Homepage section
          Expanded(
            child: JobWidgetFactory.createHomepageSection(
              title: 'All Jobs',
              maxItems: 12,
              primaryColor: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }
}
```

## Customization Options

### JobWidgetConfig Parameters

- `title`: Widget title (optional)
- `showSearchBar`: Enable/disable search functionality
- `showFilters`: Enable/disable filter dropdowns
- `maxItems`: Limit number of jobs displayed
- `primaryColor`: Custom primary color for the widget
- `backgroundColor`: Custom background color
- `height`: Fixed height for the widget
- `width`: Fixed width for the widget
- `padding`: Internal padding
- `margin`: External margin

### Theme Customization

The widgets automatically adapt to your app's theme, but you can also provide custom colors:

```dart
EmbeddableJobWidget(
  config: JobWidgetConfig(
    title: 'Custom Jobs',
    primaryColor: Colors.deepPurple,
    backgroundColor: Colors.grey[50],
    padding: EdgeInsets.all(16),
    height: 400,
  ),
)
```

## Data Format

The application expects job data in the following JSON format:

```json
[
  {
    "title": "Software Engineer",
    "organization": "Tech Company",
    "employmentType": "job",
    "jobLocation": "Cape Town, South Africa",
    "baseSalary": "Market Related",
    "closingDate": "31-07-2025",
    "image": "https://example.com/logo.jpg",
    "applyUrl": "https://example.com/apply",
    "description": "Job description here..."
  }
]
```

## Installation

1. Add the required dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0
  url_launcher: ^6.2.1
  cached_network_image: ^3.3.0
```

2. Add the job data file to your assets:

```yaml
flutter:
  assets:
    - lib/jobs/
```

3. Copy the JobStack widgets and models to your project
4. Import and use the widgets as shown in the examples above

## Web Embedding

For web applications, you can embed the Flutter widget in HTML:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Job Portal</title>
</head>
<body>
    <div id="job-widget" style="height: 600px;"></div>
    <script src="main.dart.js"></script>
</body>
</html>
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.
