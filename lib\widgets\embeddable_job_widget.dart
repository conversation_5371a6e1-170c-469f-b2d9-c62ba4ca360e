import 'package:flutter/material.dart';
import 'job_directory.dart';

/// Configuration class for the embeddable job widget
class JobWidgetConfig {
  final String? title;
  final bool showSearchBar;
  final bool showFilters;
  final int? maxItems;
  final Color? primaryColor;
  final Color? backgroundColor;
  final double? height;
  final double? width;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const JobWidgetConfig({
    this.title,
    this.showSearchBar = true,
    this.showFilters = true,
    this.maxItems,
    this.primaryColor,
    this.backgroundColor,
    this.height,
    this.width,
    this.padding,
    this.margin,
  });
}

/// A self-contained, embeddable widget for displaying job postings
/// This widget can be easily embedded in other Flutter applications or web pages
class EmbeddableJobWidget extends StatelessWidget {
  final JobWidgetConfig config;

  const EmbeddableJobWidget({
    super.key,
    this.config = const JobWidgetConfig(),
  });

  /// Factory constructor for creating a minimal job widget (no search/filters)
  factory EmbeddableJobWidget.minimal({
    String? title,
    int? maxItems,
    Color? primaryColor,
    double? height,
  }) {
    return EmbeddableJobWidget(
      config: JobWidgetConfig(
        title: title,
        showSearchBar: false,
        showFilters: false,
        maxItems: maxItems,
        primaryColor: primaryColor,
        height: height,
      ),
    );
  }

  /// Factory constructor for creating a compact job widget (search only)
  factory EmbeddableJobWidget.compact({
    String? title,
    int? maxItems,
    Color? primaryColor,
    double? height,
  }) {
    return EmbeddableJobWidget(
      config: JobWidgetConfig(
        title: title,
        showSearchBar: true,
        showFilters: false,
        maxItems: maxItems,
        primaryColor: primaryColor,
        height: height,
      ),
    );
  }

  /// Factory constructor for creating a full-featured job widget
  factory EmbeddableJobWidget.full({
    String? title,
    Color? primaryColor,
    double? height,
  }) {
    return EmbeddableJobWidget(
      config: JobWidgetConfig(
        title: title,
        showSearchBar: true,
        showFilters: true,
        primaryColor: primaryColor,
        height: height,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget child = Container(
      height: config.height,
      width: config.width,
      padding: config.padding,
      margin: config.margin,
      decoration: BoxDecoration(
        color: config.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: JobDirectory(
        title: config.title,
        showSearchBar: config.showSearchBar,
        showFilters: config.showFilters,
        maxItems: config.maxItems,
      ),
    );

    // Apply custom theme if primary color is specified
    if (config.primaryColor != null) {
      child = Theme(
        data: Theme.of(context).copyWith(
          colorScheme: Theme.of(context).colorScheme.copyWith(
            primary: config.primaryColor,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: config.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        child: child,
      );
    }

    return child;
  }
}

/// A standalone app widget that can be used as a complete job portal application
class JobPortalApp extends StatelessWidget {
  final String title;
  final Color? primaryColor;
  final bool darkMode;

  const JobPortalApp({
    super.key,
    this.title = 'Job Portal',
    this.primaryColor,
    this.darkMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: title,
      debugShowCheckedModeBanner: false,
      theme: _buildTheme(false),
      darkTheme: _buildTheme(true),
      themeMode: darkMode ? ThemeMode.dark : ThemeMode.light,
      home: Scaffold(
        appBar: AppBar(
          title: Text(title),
          backgroundColor: primaryColor ?? Colors.blue,
          foregroundColor: Colors.white,
          elevation: 2,
        ),
        body: const JobDirectory(
          title: null, // Title is shown in AppBar
        ),
      ),
    );
  }

  ThemeData _buildTheme(bool isDark) {
    final ColorScheme colorScheme = isDark
        ? ColorScheme.fromSeed(
            seedColor: primaryColor ?? Colors.blue,
            brightness: Brightness.dark,
          )
        : ColorScheme.fromSeed(
            seedColor: primaryColor ?? Colors.blue,
            brightness: Brightness.light,
          );

    return ThemeData(
      colorScheme: colorScheme,
      useMaterial3: true,
      appBarTheme: AppBarTheme(
        backgroundColor: primaryColor ?? Colors.blue,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor ?? Colors.blue,
          foregroundColor: Colors.white,
        ),
      ),
      cardTheme: const CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),
    );
  }
}

/// Utility class for creating pre-configured job widgets for common use cases
class JobWidgetFactory {
  /// Creates a widget suitable for embedding in a sidebar
  static Widget createSidebarWidget({
    String title = 'Latest Jobs',
    int maxItems = 5,
    Color? primaryColor,
  }) {
    return EmbeddableJobWidget.minimal(
      title: title,
      maxItems: maxItems,
      primaryColor: primaryColor,
      height: 400,
    );
  }

  /// Creates a widget suitable for embedding in a homepage section
  static Widget createHomepageSection({
    String title = 'Featured Jobs',
    int maxItems = 6,
    Color? primaryColor,
  }) {
    return EmbeddableJobWidget.compact(
      title: title,
      maxItems: maxItems,
      primaryColor: primaryColor,
      height: 600,
    );
  }

  /// Creates a full-page job portal widget
  static Widget createFullPortal({
    String title = 'Job Directory',
    Color? primaryColor,
  }) {
    return EmbeddableJobWidget.full(
      title: title,
      primaryColor: primaryColor,
    );
  }

  /// Creates a widget suitable for embedding in a modal or dialog
  static Widget createModalWidget({
    String title = 'Browse Jobs',
    Color? primaryColor,
  }) {
    return EmbeddableJobWidget(
      config: JobWidgetConfig(
        title: title,
        showSearchBar: true,
        showFilters: true,
        primaryColor: primaryColor,
        height: 500,
        padding: const EdgeInsets.all(16),
      ),
    );
  }
}
