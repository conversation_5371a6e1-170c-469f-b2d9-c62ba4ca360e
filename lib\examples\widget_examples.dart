import 'package:flutter/material.dart';
import '../widgets/embeddable_job_widget.dart';

/// Example file demonstrating different ways to use the embeddable job widgets
class JobWidgetExamples extends StatelessWidget {
  const JobWidgetExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Widget Examples'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('1. Minimal Widget (Sidebar Style)'),
            _buildMinimalExample(),
            
            const SizedBox(height: 32),
            _buildSectionTitle('2. Compact Widget (Homepage Section)'),
            _buildCompactExample(),
            
            const SizedBox(height: 32),
            _buildSectionTitle('3. Full Featured Widget'),
            _buildFullExample(),
            
            const SizedBox(height: 32),
            _buildSectionTitle('4. Custom Styled Widget'),
            _buildCustomExample(),
            
            const SizedBox(height: 32),
            _buildSectionTitle('5. Factory Method Examples'),
            _buildFactoryExamples(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildMinimalExample() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: EmbeddableJobWidget.minimal(
        title: 'Recent Jobs',
        maxItems: 3,
        height: 300,
      ),
    );
  }

  Widget _buildCompactExample() {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: EmbeddableJobWidget.compact(
        title: 'Featured Jobs',
        maxItems: 5,
        primaryColor: Colors.green,
        height: 400,
      ),
    );
  }

  Widget _buildFullExample() {
    return Container(
      height: 500,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: EmbeddableJobWidget.full(
        title: 'All Jobs',
        primaryColor: Colors.purple,
        height: 500,
      ),
    );
  }

  Widget _buildCustomExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: EmbeddableJobWidget(
        config: JobWidgetConfig(
          title: 'Custom Styled Jobs',
          showSearchBar: true,
          showFilters: false,
          maxItems: 4,
          primaryColor: Colors.orange,
          backgroundColor: Colors.orange[50],
          height: 400,
          padding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildFactoryExamples() {
    return Column(
      children: [
        // Sidebar widget example
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: JobWidgetFactory.createSidebarWidget(
                  title: 'Sidebar Jobs',
                  maxItems: 3,
                  primaryColor: Colors.teal,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: Container(
                height: 400,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: JobWidgetFactory.createHomepageSection(
                  title: 'Main Content Jobs',
                  maxItems: 6,
                  primaryColor: Colors.indigo,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Modal widget example
        Container(
          height: 300,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: JobWidgetFactory.createModalWidget(
            title: 'Modal Jobs',
            primaryColor: Colors.red,
          ),
        ),
      ],
    );
  }
}

/// Example of embedding job widgets in a typical app layout
class AppLayoutExample extends StatelessWidget {
  const AppLayoutExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Layout Example'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Row(
        children: [
          // Sidebar
          Container(
            width: 300,
            color: Colors.grey[100],
            child: JobWidgetFactory.createSidebarWidget(
              title: 'Quick Jobs',
              maxItems: 5,
              primaryColor: Colors.blue,
            ),
          ),
          
          // Main content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Header
                  Container(
                    height: 100,
                    width: double.infinity,
                    color: Colors.blue[50],
                    child: const Center(
                      child: Text(
                        'Main Content Area',
                        style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Job section
                  Expanded(
                    child: JobWidgetFactory.createHomepageSection(
                      title: 'Featured Opportunities',
                      maxItems: 10,
                      primaryColor: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Example showing how to use the job widgets in different screen sizes
class ResponsiveExample extends StatelessWidget {
  const ResponsiveExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Responsive Example'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          if (constraints.maxWidth > 800) {
            // Desktop layout
            return _buildDesktopLayout();
          } else if (constraints.maxWidth > 600) {
            // Tablet layout
            return _buildTabletLayout();
          } else {
            // Mobile layout
            return _buildMobileLayout();
          }
        },
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: JobWidgetFactory.createSidebarWidget(
            title: 'Quick Apply',
            maxItems: 5,
          ),
        ),
        Expanded(
          flex: 3,
          child: JobWidgetFactory.createFullPortal(
            title: 'All Jobs',
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return JobWidgetFactory.createHomepageSection(
      title: 'Job Directory',
      maxItems: 15,
    );
  }

  Widget _buildMobileLayout() {
    return JobWidgetFactory.createSidebarWidget(
      title: 'Jobs',
      maxItems: 10,
    );
  }
}
