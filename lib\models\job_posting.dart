/// Model class representing a job posting
class JobPosting {
  final String title;
  final String organization;
  final String employmentType;
  final String jobLocation;
  final String baseSalary;
  final String closingDate;
  final String image;
  final String applyUrl;
  final String description;

  const JobPosting({
    required this.title,
    required this.organization,
    required this.employmentType,
    required this.jobLocation,
    required this.baseSalary,
    required this.closingDate,
    required this.image,
    required this.applyUrl,
    required this.description,
  });

  /// Creates a JobPosting from a JSON map
  factory JobPosting.fromJson(Map<String, dynamic> json) {
    return JobPosting(
      title: json['title'] ?? '',
      organization: json['organization'] ?? '',
      employmentType: json['employmentType'] ?? '',
      jobLocation: json['jobLocation'] ?? '',
      baseSalary: json['baseSalary'] ?? '',
      closingDate: json['closingDate'] ?? '',
      image: json['image'] ?? '',
      applyUrl: json['applyUrl'] ?? '',
      description: json['description'] ?? '',
    );
  }

  /// Converts the JobPosting to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'organization': organization,
      'employmentType': employmentType,
      'jobLocation': jobLocation,
      'baseSalary': baseSalary,
      'closingDate': closingDate,
      'image': image,
      'applyUrl': applyUrl,
      'description': description,
    };
  }

  /// Helper method to get formatted location
  String get formattedLocation {
    // Clean up location string by removing extra commas and spaces
    return jobLocation
        .split(',')
        .map((part) => part.trim())
        .where((part) => part.isNotEmpty)
        .join(', ');
  }

  /// Helper method to check if closing date is soon (within 7 days)
  bool get isClosingSoon {
    try {
      final parts = closingDate.split('-');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        final closingDateTime = DateTime(year, month, day);
        final now = DateTime.now();
        final difference = closingDateTime.difference(now).inDays;
        return difference <= 7 && difference >= 0;
      }
    } catch (e) {
      // If date parsing fails, assume not closing soon
    }
    return false;
  }

  /// Helper method to get a short description (first 150 characters)
  String get shortDescription {
    if (description.length <= 150) {
      return description;
    }
    return '${description.substring(0, 147)}...';
  }

  /// Get job category based on title and description
  String get category {
    final titleLower = title.toLowerCase();
    final descLower = description.toLowerCase();
    final orgLower = organization.toLowerCase();

    // IT/Technology - Enhanced detection
    if (titleLower.contains('developer') || titleLower.contains('engineer') ||
        titleLower.contains('programmer') || titleLower.contains('software') ||
        titleLower.contains('data') || titleLower.contains('analyst') ||
        titleLower.contains('tech') || titleLower.contains('it ') ||
        titleLower.contains('automation') || titleLower.contains('devops') ||
        titleLower.contains('web') || titleLower.contains('mobile') ||
        titleLower.contains('frontend') || titleLower.contains('backend') ||
        titleLower.contains('fullstack') || titleLower.contains('full stack') ||
        titleLower.contains('database') || titleLower.contains('system') ||
        titleLower.contains('network') || titleLower.contains('cloud') ||
        titleLower.contains('cyber') || titleLower.contains('security') ||
        titleLower.contains('qa') || titleLower.contains('testing') ||
        titleLower.contains('scrum') || titleLower.contains('agile') ||
        descLower.contains('programming') || descLower.contains('coding') ||
        descLower.contains('software') || descLower.contains('technical') ||
        descLower.contains('development') || descLower.contains('javascript') ||
        descLower.contains('python') || descLower.contains('java') ||
        descLower.contains('react') || descLower.contains('angular') ||
        descLower.contains('node') || descLower.contains('sql') ||
        descLower.contains('database') || descLower.contains('api') ||
        orgLower.contains('software') || orgLower.contains('tech') ||
        orgLower.contains('digital') || orgLower.contains('systems')) {
      return 'IT/Technology';
    }

    // Finance & Banking
    if (titleLower.contains('finance') || titleLower.contains('accounting') ||
        titleLower.contains('investment') || titleLower.contains('banking') ||
        titleLower.contains('compliance') || titleLower.contains('audit') ||
        titleLower.contains('risk') || titleLower.contains('treasury') ||
        titleLower.contains('credit') || titleLower.contains('loan') ||
        titleLower.contains('financial') || titleLower.contains('economist') ||
        descLower.contains('financial') || descLower.contains('investment') ||
        descLower.contains('banking') || descLower.contains('accounting') ||
        orgLower.contains('bank') || orgLower.contains('financial') ||
        orgLower.contains('investment') || orgLower.contains('mutual')) {
      return 'Finance';
    }

    // Healthcare & Medical
    if (titleLower.contains('health') || titleLower.contains('medical') ||
        titleLower.contains('nurse') || titleLower.contains('doctor') ||
        titleLower.contains('clinical') || titleLower.contains('pharmacy') ||
        titleLower.contains('therapist') || titleLower.contains('dentist') ||
        descLower.contains('healthcare') || descLower.contains('medical') ||
        descLower.contains('patient') || descLower.contains('clinical') ||
        orgLower.contains('health') || orgLower.contains('medical') ||
        orgLower.contains('hospital') || orgLower.contains('clinic')) {
      return 'Healthcare';
    }

    // Sales, Marketing & Business Development
    if (titleLower.contains('sales') || titleLower.contains('marketing') ||
        titleLower.contains('business') || titleLower.contains('commercial') ||
        titleLower.contains('account') || titleLower.contains('client') ||
        titleLower.contains('relationship') || titleLower.contains('revenue') ||
        descLower.contains('sales') || descLower.contains('marketing') ||
        descLower.contains('business development') || descLower.contains('client') ||
        descLower.contains('customer acquisition') || descLower.contains('revenue')) {
      return 'Sales/Marketing';
    }

    // Education
    if (titleLower.contains('teacher') || titleLower.contains('education') ||
        titleLower.contains('instructor') || descLower.contains('teaching') ||
        descLower.contains('education') || descLower.contains('training')) {
      return 'Education';
    }

    // Management & Leadership
    if (titleLower.contains('manager') || titleLower.contains('director') ||
        titleLower.contains('supervisor') || titleLower.contains('lead') ||
        titleLower.contains('coordinator') || titleLower.contains('head') ||
        titleLower.contains('chief') || titleLower.contains('executive') ||
        titleLower.contains('principal') || titleLower.contains('senior') ||
        descLower.contains('management') || descLower.contains('leadership') ||
        descLower.contains('team lead') || descLower.contains('supervise')) {
      return 'Management';
    }

    // Engineering (Non-IT)
    if (titleLower.contains('civil') || titleLower.contains('mechanical') ||
        titleLower.contains('electrical') || titleLower.contains('construction') ||
        titleLower.contains('architect') || titleLower.contains('structural') ||
        titleLower.contains('industrial') || titleLower.contains('chemical') ||
        descLower.contains('construction') || descLower.contains('engineering') ||
        descLower.contains('design') || descLower.contains('blueprint')) {
      return 'Engineering';
    }

    // Customer Service & Support
    if (titleLower.contains('customer') || titleLower.contains('service') ||
        titleLower.contains('support') || titleLower.contains('receptionist') ||
        titleLower.contains('call center') || titleLower.contains('help desk') ||
        titleLower.contains('client service') || titleLower.contains('contact center') ||
        descLower.contains('customer service') || descLower.contains('customer support') ||
        descLower.contains('help customers') || descLower.contains('client support')) {
      return 'Customer Service';
    }

    // Operations/Logistics
    if (titleLower.contains('operations') || titleLower.contains('logistics') ||
        titleLower.contains('supply') || titleLower.contains('warehouse') ||
        descLower.contains('operations') || descLower.contains('logistics')) {
      return 'Operations';
    }

    // Quality Assurance
    if (titleLower.contains('qa') || titleLower.contains('quality') ||
        titleLower.contains('test') || descLower.contains('quality assurance') ||
        descLower.contains('testing')) {
      return 'Quality Assurance';
    }

    // Security & Safety
    if (titleLower.contains('security') || titleLower.contains('cyber') ||
        titleLower.contains('safety') || titleLower.contains('guard') ||
        descLower.contains('security') || descLower.contains('cybersecurity') ||
        descLower.contains('safety') || descLower.contains('protection')) {
      return 'Security';
    }

    // Human Resources
    if (titleLower.contains('hr') || titleLower.contains('human resources') ||
        titleLower.contains('recruitment') || titleLower.contains('recruiter') ||
        titleLower.contains('talent') || titleLower.contains('people') ||
        descLower.contains('human resources') || descLower.contains('recruitment') ||
        descLower.contains('hiring') || descLower.contains('employee')) {
      return 'Human Resources';
    }

    // Legal & Compliance
    if (titleLower.contains('legal') || titleLower.contains('lawyer') ||
        titleLower.contains('attorney') || titleLower.contains('compliance') ||
        titleLower.contains('regulatory') || titleLower.contains('paralegal') ||
        descLower.contains('legal') || descLower.contains('compliance') ||
        descLower.contains('regulatory') || descLower.contains('law')) {
      return 'Legal';
    }

    // Administrative & Office
    if (titleLower.contains('admin') || titleLower.contains('assistant') ||
        titleLower.contains('secretary') || titleLower.contains('clerk') ||
        titleLower.contains('office') || titleLower.contains('administrative') ||
        descLower.contains('administrative') || descLower.contains('office') ||
        descLower.contains('clerical') || descLower.contains('filing')) {
      return 'Administrative';
    }

    // Default category
    return 'Other';
  }

  @override
  String toString() {
    return 'JobPosting(title: $title, organization: $organization, location: $jobLocation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JobPosting &&
        other.title == title &&
        other.organization == organization &&
        other.applyUrl == applyUrl;
  }

  @override
  int get hashCode {
    return title.hashCode ^ organization.hashCode ^ applyUrl.hashCode;
  }
}
