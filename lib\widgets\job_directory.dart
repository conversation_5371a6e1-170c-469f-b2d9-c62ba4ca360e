import 'package:flutter/material.dart';
import '../models/job_posting.dart';
import '../services/job_data_service.dart';
import 'job_card.dart';

/// Main directory widget that displays job postings in a searchable, filterable layout
class JobDirectory extends StatefulWidget {
  final bool showSearchBar;
  final bool showFilters;
  final int? maxItems;
  final String? title;

  const JobDirectory({
    super.key,
    this.showSearchBar = true,
    this.showFilters = true,
    this.maxItems,
    this.title,
  });

  @override
  State<JobDirectory> createState() => _JobDirectoryState();
}

class _JobDirectoryState extends State<JobDirectory> {
  List<JobPosting> _allJobs = [];
  List<JobPosting> _filteredJobs = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedLocation = 'All Locations';
  String _selectedOrganization = 'All Organizations';
  String _sortBy = 'Recent';
  
  List<String> _locations = ['All Locations'];
  List<String> _organizations = ['All Organizations'];

  @override
  void initState() {
    super.initState();
    _loadJobs();
  }

  Future<void> _loadJobs() async {
    try {
      final jobs = await JobDataService.loadJobPostings();
      setState(() {
        _allJobs = jobs;
        _filteredJobs = jobs;
        _isLoading = false;
        
        // Populate filter options
        _locations = ['All Locations', ...JobDataService.getUniqueLocations(jobs)];
        _organizations = ['All Organizations', ...JobDataService.getUniqueOrganizations(jobs)];
      });
      _applyFilters();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading jobs: $e')),
        );
      }
    }
  }

  void _applyFilters() {
    List<JobPosting> filtered = List.from(_allJobs);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = JobDataService.filterJobs(filtered, _searchQuery);
    }

    // Apply location filter
    if (_selectedLocation != 'All Locations') {
      filtered = filtered.where((job) => job.formattedLocation == _selectedLocation).toList();
    }

    // Apply organization filter
    if (_selectedOrganization != 'All Organizations') {
      filtered = filtered.where((job) => job.organization == _selectedOrganization).toList();
    }

    // Apply sorting
    switch (_sortBy) {
      case 'Recent':
        // Keep original order (most recent first)
        break;
      case 'Closing Date':
        filtered = JobDataService.sortJobsByClosingDate(filtered);
        break;
      case 'Company A-Z':
        filtered.sort((a, b) => a.organization.compareTo(b.organization));
        break;
      case 'Location':
        filtered.sort((a, b) => a.formattedLocation.compareTo(b.formattedLocation));
        break;
    }

    // Apply max items limit if specified
    if (widget.maxItems != null && filtered.length > widget.maxItems!) {
      filtered = filtered.take(widget.maxItems!).toList();
    }

    setState(() {
      _filteredJobs = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              widget.title!,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        // Search bar
        if (widget.showSearchBar) _buildSearchBar(),

        // Filters
        if (widget.showFilters) _buildFilters(),

        // Job count and sorting
        _buildJobCountAndSort(),

        // Job listings
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredJobs.isEmpty
                  ? _buildEmptyState()
                  : _buildJobList(),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search jobs, companies, or locations...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          filled: true,
          fillColor: Colors.grey[50],
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          _applyFilters();
        },
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Wrap(
        spacing: 8.0,
        children: [
          // Location filter
          DropdownButton<String>(
            value: _selectedLocation,
            items: _locations.map((location) {
              return DropdownMenuItem(
                value: location,
                child: Text(location, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedLocation = value!;
              });
              _applyFilters();
            },
          ),
          const SizedBox(width: 16),
          
          // Organization filter
          DropdownButton<String>(
            value: _selectedOrganization,
            items: _organizations.map((org) {
              return DropdownMenuItem(
                value: org,
                child: Text(org, overflow: TextOverflow.ellipsis),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedOrganization = value!;
              });
              _applyFilters();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildJobCountAndSort() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${_filteredJobs.length} jobs found',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          DropdownButton<String>(
            value: _sortBy,
            items: ['Recent', 'Closing Date', 'Company A-Z', 'Location']
                .map((sort) => DropdownMenuItem(
                      value: sort,
                      child: Text('Sort by $sort'),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _sortBy = value!;
              });
              _applyFilters();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildJobList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      itemCount: _filteredJobs.length,
      itemBuilder: (context, index) {
        return JobCard(job: _filteredJobs[index]);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No jobs found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
