import 'package:flutter/foundation.dart';
import '../models/job_posting.dart';

/// Service for managing favorite jobs with persistence
class FavoritesService extends ChangeNotifier {
  static final FavoritesService _instance = FavoritesService._internal();
  factory FavoritesService() => _instance;
  FavoritesService._internal();

  final Set<String> _favoriteJobIds = <String>{};
  final List<JobPosting> _favoriteJobs = <JobPosting>[];

  /// Get all favorite job IDs
  Set<String> get favoriteJobIds => Set.from(_favoriteJobIds);

  /// Get all favorite jobs
  List<JobPosting> get favoriteJobs => List.from(_favoriteJobs);

  /// Check if a job is favorited
  bool isFavorite(JobPosting job) {
    return _favoriteJobIds.contains(_getJobId(job));
  }

  /// Add a job to favorites
  void addFavorite(JobPosting job) {
    final jobId = _getJobId(job);
    if (!_favoriteJobIds.contains(jobId)) {
      _favoriteJobIds.add(jobId);
      _favoriteJobs.add(job);
      _saveFavorites();
      notifyListeners();
    }
  }

  /// Remove a job from favorites
  void removeFavorite(JobPosting job) {
    final jobId = _getJobId(job);
    if (_favoriteJobIds.contains(jobId)) {
      _favoriteJobIds.remove(jobId);
      _favoriteJobs.removeWhere((favJob) => _getJobId(favJob) == jobId);
      _saveFavorites();
      notifyListeners();
    }
  }

  /// Toggle favorite status of a job
  bool toggleFavorite(JobPosting job) {
    if (isFavorite(job)) {
      removeFavorite(job);
      return false;
    } else {
      addFavorite(job);
      return true;
    }
  }

  /// Clear all favorites
  void clearFavorites() {
    _favoriteJobIds.clear();
    _favoriteJobs.clear();
    _saveFavorites();
    notifyListeners();
  }

  /// Get the number of favorite jobs
  int get favoriteCount => _favoriteJobIds.length;

  /// Generate a unique ID for a job posting
  String _getJobId(JobPosting job) {
    // Use a combination of title, organization, and apply URL for uniqueness
    return '${job.title}_${job.organization}_${job.applyUrl}'.hashCode.toString();
  }

  /// Save favorites to local storage (simplified for demo)
  void _saveFavorites() {
    // In a real app, you would save to SharedPreferences or a database
    // For now, we'll just keep them in memory
    debugPrint('Saved ${_favoriteJobIds.length} favorites');
  }

  /// Load favorites from local storage (simplified for demo)
  void _loadFavorites() {
    // In a real app, you would load from SharedPreferences or a database
    // For now, we'll start with empty favorites
    debugPrint('Loaded favorites');
  }

  /// Initialize the service
  void initialize() {
    _loadFavorites();
  }

  /// Update favorite jobs list when new jobs are loaded
  void updateJobsList(List<JobPosting> allJobs) {
    // Update the favorite jobs list with current job data
    _favoriteJobs.clear();
    for (final job in allJobs) {
      if (_favoriteJobIds.contains(_getJobId(job))) {
        _favoriteJobs.add(job);
      }
    }
    notifyListeners();
  }

  /// Get favorite jobs filtered by search term
  List<JobPosting> searchFavorites(String searchTerm) {
    if (searchTerm.isEmpty) {
      return favoriteJobs;
    }

    final lowerSearchTerm = searchTerm.toLowerCase();
    return _favoriteJobs.where((job) {
      return job.title.toLowerCase().contains(lowerSearchTerm) ||
             job.organization.toLowerCase().contains(lowerSearchTerm) ||
             job.jobLocation.toLowerCase().contains(lowerSearchTerm) ||
             job.description.toLowerCase().contains(lowerSearchTerm);
    }).toList();
  }

  /// Get favorites by category
  List<JobPosting> getFavoritesByCategory(String category) {
    if (category == 'All Categories') {
      return favoriteJobs;
    }
    
    return _favoriteJobs.where((job) {
      return _getJobCategory(job) == category;
    }).toList();
  }

  /// Get job category based on title and description
  String _getJobCategory(JobPosting job) {
    final title = job.title.toLowerCase();
    final description = job.description.toLowerCase();
    
    // IT/Technology
    if (title.contains('developer') || title.contains('engineer') || 
        title.contains('programmer') || title.contains('software') ||
        title.contains('data') || title.contains('analyst') ||
        title.contains('tech') || title.contains('it ') ||
        description.contains('programming') || description.contains('coding')) {
      return 'IT/Technology';
    }
    
    // Finance
    if (title.contains('finance') || title.contains('accounting') ||
        title.contains('analyst') || title.contains('investment') ||
        title.contains('banking') || description.contains('financial')) {
      return 'Finance';
    }
    
    // Healthcare
    if (title.contains('health') || title.contains('medical') ||
        title.contains('nurse') || title.contains('doctor') ||
        description.contains('healthcare') || description.contains('medical')) {
      return 'Healthcare';
    }
    
    // Sales/Marketing
    if (title.contains('sales') || title.contains('marketing') ||
        title.contains('business') || description.contains('sales') ||
        description.contains('marketing')) {
      return 'Sales/Marketing';
    }
    
    // Education
    if (title.contains('teacher') || title.contains('education') ||
        title.contains('instructor') || description.contains('teaching') ||
        description.contains('education')) {
      return 'Education';
    }
    
    // Management
    if (title.contains('manager') || title.contains('director') ||
        title.contains('supervisor') || title.contains('lead') ||
        description.contains('management') || description.contains('leadership')) {
      return 'Management';
    }
    
    // Construction/Engineering
    if (title.contains('engineer') || title.contains('construction') ||
        title.contains('architect') || title.contains('civil') ||
        description.contains('construction') || description.contains('engineering')) {
      return 'Engineering';
    }
    
    // Customer Service
    if (title.contains('customer') || title.contains('service') ||
        title.contains('support') || description.contains('customer service')) {
      return 'Customer Service';
    }
    
    // Operations
    if (title.contains('operations') || title.contains('logistics') ||
        title.contains('supply') || description.contains('operations')) {
      return 'Operations';
    }
    
    // Default category
    return 'Other';
  }
}
