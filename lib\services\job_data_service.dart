import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/job_posting.dart';

/// Service class for loading and processing job data
class JobDataService {
  static const String _jobsFilePath = 'lib/jobs/jobs_2025-07-02T16-35-43-444Z.json';
  
  /// Loads job postings from the JSON asset file
  static Future<List<JobPosting>> loadJobPostings() async {
    try {
      // Load the JSON file from assets
      final String jsonString = await rootBundle.loadString(_jobsFilePath);
      
      // Parse the JSON
      final List<dynamic> jsonList = json.decode(jsonString);
      
      // Convert to JobPosting objects
      final List<JobPosting> jobPostings = jsonList
          .map((json) => JobPosting.fromJson(json as Map<String, dynamic>))
          .toList();
      
      return jobPostings;
    } catch (e) {
      throw Exception('Failed to load job postings: $e');
    }
  }
  
  /// Filters job postings by search term (searches title, organization, and location)
  static List<JobPosting> filterJobs(List<JobPosting> jobs, String searchTerm) {
    if (searchTerm.isEmpty) {
      return jobs;
    }
    
    final String lowerSearchTerm = searchTerm.toLowerCase();
    
    return jobs.where((job) {
      return job.title.toLowerCase().contains(lowerSearchTerm) ||
             job.organization.toLowerCase().contains(lowerSearchTerm) ||
             job.jobLocation.toLowerCase().contains(lowerSearchTerm) ||
             job.description.toLowerCase().contains(lowerSearchTerm);
    }).toList();
  }
  
  /// Groups job postings by location
  static Map<String, List<JobPosting>> groupJobsByLocation(List<JobPosting> jobs) {
    final Map<String, List<JobPosting>> groupedJobs = {};
    
    for (final job in jobs) {
      final String location = job.formattedLocation;
      if (!groupedJobs.containsKey(location)) {
        groupedJobs[location] = [];
      }
      groupedJobs[location]!.add(job);
    }
    
    return groupedJobs;
  }
  
  /// Groups job postings by organization
  static Map<String, List<JobPosting>> groupJobsByOrganization(List<JobPosting> jobs) {
    final Map<String, List<JobPosting>> groupedJobs = {};
    
    for (final job in jobs) {
      final String organization = job.organization;
      if (!groupedJobs.containsKey(organization)) {
        groupedJobs[organization] = [];
      }
      groupedJobs[organization]!.add(job);
    }
    
    return groupedJobs;
  }
  
  /// Sorts job postings by closing date (earliest first)
  static List<JobPosting> sortJobsByClosingDate(List<JobPosting> jobs) {
    final List<JobPosting> sortedJobs = List.from(jobs);
    
    sortedJobs.sort((a, b) {
      try {
        final DateTime dateA = _parseDate(a.closingDate);
        final DateTime dateB = _parseDate(b.closingDate);
        return dateA.compareTo(dateB);
      } catch (e) {
        // If date parsing fails, maintain original order
        return 0;
      }
    });
    
    return sortedJobs;
  }
  
  /// Gets jobs that are closing soon (within 7 days)
  static List<JobPosting> getJobsClosingSoon(List<JobPosting> jobs) {
    return jobs.where((job) => job.isClosingSoon).toList();
  }
  
  /// Helper method to parse date string in DD-MM-YYYY format
  static DateTime _parseDate(String dateString) {
    final parts = dateString.split('-');
    if (parts.length == 3) {
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);
      return DateTime(year, month, day);
    }
    throw FormatException('Invalid date format: $dateString');
  }
  
  /// Gets unique locations from job postings
  static List<String> getUniqueLocations(List<JobPosting> jobs) {
    final Set<String> locations = jobs
        .map((job) => job.formattedLocation)
        .where((location) => location.isNotEmpty)
        .toSet();
    
    final List<String> sortedLocations = locations.toList();
    sortedLocations.sort();
    return sortedLocations;
  }
  
  /// Gets unique organizations from job postings
  static List<String> getUniqueOrganizations(List<JobPosting> jobs) {
    final Set<String> organizations = jobs
        .map((job) => job.organization)
        .where((org) => org.isNotEmpty)
        .toSet();

    final List<String> sortedOrganizations = organizations.toList();
    sortedOrganizations.sort();
    return sortedOrganizations;
  }

  /// Gets unique categories from job postings
  static List<String> getUniqueCategories(List<JobPosting> jobs) {
    final Set<String> categories = jobs
        .map((job) => job.category)
        .where((category) => category.isNotEmpty)
        .toSet();

    final List<String> sortedCategories = categories.toList();
    sortedCategories.sort();
    return sortedCategories;
  }

  /// Groups job postings by category
  static Map<String, List<JobPosting>> groupJobsByCategory(List<JobPosting> jobs) {
    final Map<String, List<JobPosting>> groupedJobs = {};

    for (final job in jobs) {
      final String category = job.category;
      if (!groupedJobs.containsKey(category)) {
        groupedJobs[category] = [];
      }
      groupedJobs[category]!.add(job);
    }

    return groupedJobs;
  }

  /// Filters job postings by category
  static List<JobPosting> filterJobsByCategory(List<JobPosting> jobs, String category) {
    if (category == 'All Categories' || category.isEmpty) {
      return jobs;
    }

    return jobs.where((job) => job.category == category).toList();
  }

  /// Gets job count by category
  static Map<String, int> getJobCountByCategory(List<JobPosting> jobs) {
    final Map<String, int> categoryCounts = {};

    for (final job in jobs) {
      final String category = job.category;
      categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
    }

    return categoryCounts;
  }

  /// Gets the most popular categories (top 5)
  static List<String> getPopularCategories(List<JobPosting> jobs) {
    final Map<String, int> categoryCounts = getJobCountByCategory(jobs);

    final List<MapEntry<String, int>> sortedEntries = categoryCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedEntries.take(5).map((entry) => entry.key).toList();
  }
}
