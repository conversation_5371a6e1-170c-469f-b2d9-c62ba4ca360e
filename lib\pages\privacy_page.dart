import 'package:flutter/material.dart';

class PrivacyPage extends StatelessWidget {
  const PrivacyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Privacy Policy',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().toString().split(' ')[0]}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            
            _buildSection(
              'Introduction',
              'JobStack ("we", "our", or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application.',
            ),
            
            _buildSection(
              'Information We Collect',
              'We may collect information about you in a variety of ways:\n\n• Personal Data: Name, email address, phone number, and other contact information you provide\n• Usage Data: Information about how you use our app, including search queries and job preferences\n• Device Data: Information about your mobile device, including device ID, operating system, and app version\n• Location Data: With your permission, we may collect location information to show relevant job listings',
            ),
            
            _buildSection(
              'How We Use Your Information',
              'We use the information we collect to:\n\n• Provide and maintain our service\n• Personalize your job search experience\n• Send you relevant job alerts and notifications\n• Improve our app and develop new features\n• Communicate with you about our services\n• Comply with legal obligations',
            ),
            
            _buildSection(
              'Information Sharing',
              'We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except in the following circumstances:\n\n• With your explicit consent\n• To comply with legal requirements\n• To protect our rights and safety\n• With service providers who assist us in operating our app\n• In connection with a business transfer or merger',
            ),
            
            _buildSection(
              'Data Security',
              'We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.',
            ),
            
            _buildSection(
              'Data Retention',
              'We retain your personal information only for as long as necessary to fulfill the purposes outlined in this Privacy Policy, unless a longer retention period is required by law.',
            ),
            
            _buildSection(
              'Your Rights',
              'Depending on your location, you may have the following rights:\n\n• Access your personal data\n• Correct inaccurate data\n• Delete your personal data\n• Restrict processing of your data\n• Data portability\n• Object to processing\n• Withdraw consent',
            ),
            
            _buildSection(
              'Cookies and Tracking',
              'Our app may use cookies and similar tracking technologies to enhance your experience. You can control cookie settings through your device preferences.',
            ),
            
            _buildSection(
              'Third-Party Services',
              'Our app may contain links to third-party websites or services. We are not responsible for the privacy practices of these third parties.',
            ),
            
            _buildSection(
              'Children\'s Privacy',
              'Our service is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.',
            ),
            
            _buildSection(
              'Changes to Privacy Policy',
              'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.',
            ),
            
            _buildSection(
              'Contact Us',
              'If you have any questions about this Privacy Policy, please contact us at:\n\nEmail: <EMAIL>\nAddress: 123 Job Street, Career City, CC 12345',
            ),
            
            const SizedBox(height: 32),
            
            Center(
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('I Understand'),
              ),
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
