# JobStack Mobile Guide

## 📱 Mobile-Optimized Features

The JobStack app has been redesigned with a mobile-first approach for better usability on smartphones and tablets.

### 🎯 Key Mobile Improvements

#### **1. Touch-Friendly Interface**
- Larger touch targets (minimum 44px)
- Improved button spacing and padding
- Swipe gestures and pull-to-refresh
- Haptic feedback for interactions

#### **2. Compact Design**
- Condensed job cards with essential information
- Collapsible filters to save screen space
- Bottom navigation for easy thumb access
- Modal bottom sheets for detailed views

#### **3. Enhanced Navigation**
- **Bottom Navigation Bar** with 3 main sections:
  - 🏢 **All Jobs**: Complete job directory with search and filters
  - ⏰ **Closing Soon**: Jobs closing within 7 days (with badge count)
  - ❤️ **Favorites**: Saved jobs (tap heart to add/remove)

#### **4. Smart Search & Filters**
- **Expandable Search Bar**: Rounded design with clear button
- **Collapsible Filters**: Tap to expand/collapse filter options
- **Quick Sort**: Easy dropdown for sorting options
- **Clear All**: One-tap filter reset

#### **5. Job Card Features**
- **Compact Mode**: Shows title, company, location in minimal space
- **Full Mode**: Detailed view with description and apply button
- **Favorite Button**: Animated heart icon with feedback
- **Closing Soon Badge**: Red indicator for urgent applications
- **Tap to Expand**: Full job details in bottom sheet modal

## 🚀 Usage Examples

### Basic Mobile App

```dart
import 'package:flutter/material.dart';
import 'package:jobstack/main.dart';

void main() {
  runApp(const JobStackApp());
}
```

### Embeddable Mobile Widgets

#### 1. Compact Widget (for sidebars/cards)
```dart
import 'package:jobstack/widgets/mobile_embeddable_widget.dart';

Widget buildJobCard() {
  return MobileEmbeddableJobWidget.compact(
    title: 'Latest Jobs',
    maxItems: 3,
    primaryColor: Colors.blue,
    height: 250,
  );
}
```

#### 2. Searchable Widget (for sections)
```dart
Widget buildJobSection() {
  return MobileEmbeddableJobWidget.searchable(
    title: 'Find Jobs',
    maxItems: 10,
    primaryColor: Colors.green,
    height: 400,
  );
}
```

#### 3. Full Mobile Portal
```dart
Widget buildFullPortal() {
  return MobileEmbeddableJobWidget.full(
    title: 'Job Portal',
    primaryColor: Colors.purple,
    showAppBar: true,
  );
}
```

### Factory Methods for Common Use Cases

```dart
import 'package:jobstack/widgets/mobile_embeddable_widget.dart';

class MyMobileApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Mobile sidebar
          MobileJobWidgetFactory.createMobileSidebar(
            title: 'Quick Jobs',
            maxItems: 3,
          ),
          
          // Mobile section
          Expanded(
            child: MobileJobWidgetFactory.createMobileSection(
              title: 'All Opportunities',
              maxItems: 15,
            ),
          ),
        ],
      ),
    );
  }
}
```

## 📐 Responsive Design

### Screen Size Adaptations

The app automatically adapts to different screen sizes:

- **Phone (< 600px)**: Single column, bottom navigation
- **Tablet (600-800px)**: Optimized spacing, larger cards
- **Desktop (> 800px)**: Falls back to desktop layout

### Custom Responsive Widget

```dart
class ResponsiveJobWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600) {
          // Mobile layout
          return MobileJobWidgetFactory.createMobilePortal();
        } else {
          // Tablet/Desktop layout
          return JobWidgetFactory.createFullPortal();
        }
      },
    );
  }
}
```

## 🎨 Customization Options

### Theme Customization

```dart
MobileEmbeddableJobWidget(
  config: MobileJobWidgetConfig(
    title: 'Custom Jobs',
    primaryColor: Colors.deepOrange,
    backgroundColor: Colors.orange[50],
    showSearchBar: true,
    showFilters: false,
    isCompact: true,
    height: 350,
    padding: EdgeInsets.all(16),
  ),
)
```

### Available Configuration Options

- `title`: Widget title
- `showSearchBar`: Enable/disable search
- `showFilters`: Enable/disable filters
- `maxItems`: Limit number of jobs
- `primaryColor`: Custom accent color
- `backgroundColor`: Custom background
- `height`: Fixed height
- `padding`: Internal padding
- `isCompact`: Use compact card layout
- `showAppBar`: Show built-in app bar

## 🔧 Integration Tips

### 1. In Existing Flutter Apps

```dart
// Add to your existing app
class HomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('My App')),
      body: Column(
        children: [
          // Your existing content
          Container(height: 200, child: MyContent()),
          
          // Add job widget
          Expanded(
            child: MobileJobWidgetFactory.createMobileSection(
              title: 'Job Opportunities',
              primaryColor: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
```

### 2. As Modal/Bottom Sheet

```dart
void showJobsModal(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => Container(
      height: MediaQuery.of(context).size.height * 0.8,
      child: MobileJobWidgetFactory.createMobileModal(
        title: 'Browse Jobs',
        primaryColor: Colors.blue,
      ),
    ),
  );
}
```

### 3. In Web Apps (Mobile View)

```dart
// Detect mobile browser
bool isMobile = MediaQuery.of(context).size.width < 600;

Widget buildJobWidget() {
  return isMobile 
    ? MobileJobWidgetFactory.createMobilePortal()
    : JobWidgetFactory.createFullPortal();
}
```

## 📱 Mobile-Specific Features

### Pull-to-Refresh
- Pull down on job list to refresh data
- Shows loading indicator during refresh
- Automatic error handling with retry

### Collapsible Filters
- Tap "Filters" to expand/collapse
- Saves screen space on mobile
- Quick access to sort and filter options

### Bottom Sheet Details
- Tap any job card to see full details
- Swipe down to dismiss
- Direct apply button at bottom

### Favorite System
- Tap heart icon to add/remove favorites
- Animated feedback on interaction
- Persistent across app sessions

### Smart Navigation
- Bottom navigation for thumb-friendly access
- Badge indicators for urgent jobs
- Smooth transitions between sections

## 🎯 Best Practices

1. **Use appropriate widget sizes** for mobile screens
2. **Enable pull-to-refresh** for better UX
3. **Customize colors** to match your app theme
4. **Test on different screen sizes** and orientations
5. **Consider network conditions** for image loading
6. **Implement proper error handling** for data loading

## 🔄 Updates and Maintenance

The mobile widgets automatically handle:
- Data loading and error states
- Image caching and fallbacks
- Responsive layout adjustments
- Touch interaction optimization
- Performance optimization for mobile devices
