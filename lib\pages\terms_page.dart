import 'package:flutter/material.dart';

class TermsPage extends StatelessWidget {
  const TermsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms of Service'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Terms of Service',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${DateTime.now().toString().split(' ')[0]}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            
            _buildSection(
              'Acceptance of Terms',
              'By accessing and using JobStack, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
            ),
            
            _buildSection(
              'Use License',
              'Permission is granted to temporarily download one copy of JobStack per device for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:\n\n• modify or copy the materials\n• use the materials for any commercial purpose or for any public display\n• attempt to reverse engineer any software contained in JobStack\n• remove any copyright or other proprietary notations from the materials',
            ),
            
            _buildSection(
              'Disclaimer',
              'The materials in JobStack are provided on an \'as is\' basis. JobStack makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.',
            ),
            
            _buildSection(
              'User Accounts',
              'When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.',
            ),
            
            _buildSection(
              'Job Listings',
              'JobStack aggregates job listings from various sources. We do not guarantee the accuracy, completeness, or availability of any job listing. Users should verify all job details directly with the employer.',
            ),
            
            _buildSection(
              'Privacy',
              'Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.',
            ),
            
            _buildSection(
              'Prohibited Uses',
              'You may not use our service:\n\n• for any unlawful purpose or to solicit others to perform unlawful acts\n• to violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances\n• to infringe upon or violate our intellectual property rights or the intellectual property rights of others\n• to harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate\n• to submit false or misleading information',
            ),
            
            _buildSection(
              'Termination',
              'We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.',
            ),
            
            _buildSection(
              'Changes to Terms',
              'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.',
            ),
            
            _buildSection(
              'Contact Information',
              'If you have any questions about these Terms of Service, please contact us at:\n\nEmail: <EMAIL>\nAddress: 123 Job Street, Career City, CC 12345',
            ),
            
            const SizedBox(height: 32),
            
            Center(
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('I Understand'),
              ),
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
