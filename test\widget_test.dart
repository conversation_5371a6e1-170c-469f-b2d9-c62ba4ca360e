// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:jobstack/main.dart';

void main() {
  testWidgets('JobStack app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const JobStackApp());

    // Verify that the app title is displayed
    expect(find.text('JobStack - Job Directory'), findsOneWidget);

    // Pump a few frames to allow initial loading
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 100));

    // Verify that loading indicator or job-related elements are present
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });
}
